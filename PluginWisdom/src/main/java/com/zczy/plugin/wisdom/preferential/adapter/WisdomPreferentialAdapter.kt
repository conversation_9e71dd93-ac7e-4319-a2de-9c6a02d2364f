package com.zczy.plugin.wisdom.preferential.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.preferential.req.RspQueryConsignorOilCashRebateList

/**
 * 功能描述: 奖励明细
 * <AUTHOR>
 * @date 2022/10/28-15:41
 */

class WisdomPreferentialAdapter : BaseQuickAdapter<RspQueryConsignorOilCashRebateList, BaseViewHolder>(R.layout.wisdom_preferential_item) {
    override fun convert(helper: BaseViewHolder?, item: RspQueryConsignorOilCashRebateList?) {
        helper?.apply {
            item?.let {
                setText(R.id.tvTitle, it.tradeRemark)
                setText(R.id.tvMoney, it.oilCashRebateMoney + "元")
                setText(R.id.tvType, it.businessTypeStr)
                setText(R.id.tvMoney, "${it.oilCashRebateMoney}元")
                setText(R.id.tvCheckNo, "复核单号：${it.checkNo}")
                setText(R.id.tvOrderId, "运单号：${it.orderId}")
                when (it.tradeType) {
                    -1 -> {
                        setGone(R.id.tvCheckNo, false)
                        setGone(R.id.tvOrderId, true)
                        setText(R.id.tvTime, "运费抵扣时间：" + it.settleTime)
                    }

                    1, 7 -> {
                        setGone(R.id.tvCheckNo, true)
                        setGone(R.id.tvOrderId, true)
                        setText(R.id.tvTime, "退回结算时间：" + it.settleTime)
                    }

                    2 -> {
                        setGone(R.id.tvCheckNo, true)
                        setGone(R.id.tvOrderId, false)
                        setText(R.id.tvTime, "结算时间：" + it.settleTime)
                    }
                }
            }
        }
    }

}