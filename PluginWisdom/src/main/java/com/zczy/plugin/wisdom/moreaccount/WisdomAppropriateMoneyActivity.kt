package com.zczy.plugin.wisdom.moreaccount

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.Editable
import android.text.TextUtils
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.base.WisdomBaseActivity
import com.zczy.plugin.wisdom.moreaccount.adapter.WisdomAppropriateMoneyAdapter
import com.zczy.plugin.wisdom.moreaccount.module.WisdomAppropriateMoneyModule
import com.zczy.plugin.wisdom.moreaccount.req.PageArrayList
import com.zczy.plugin.wisdom.moreaccount.req.ReqWisdomAppropriateMoneyTransfer
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomAppropriateMoney
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomAppropriateSelectAccount2

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/513:24
 *    desc   : 资金划拨页面
 *    version: 1.0
 */
open class WisdomAppropriateMoneyActivity : WisdomBaseActivity<WisdomAppropriateMoneyModule>(), View.OnClickListener, BaseQuickAdapter.OnItemClickListener {

    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val recyclerView by lazy { findViewById<RecyclerView>(R.id.recycler_view) }
    private val etInputMoney by lazy { findViewById<EditText>(R.id.et_input_money) }
    private val tvAllMoney by lazy { findViewById<TextView>(R.id.tvAllMoney) }
    private val tvCommit by lazy { findViewById<TextView>(R.id.tv_commit) }
    private val tvShortName by lazy { findViewById<TextView>(R.id.tvShortName) }
    private val tvDepositMoney by lazy { findViewById<TextView>(R.id.tv_deposit_money) }
    private val ivCustomerService by lazy { findViewById<ImageView>(R.id.iv_customer_service) }
    private val mAdapter = WisdomAppropriateMoneyAdapter()
    private var rspWisdomAppropriateSelectAccount2: RspWisdomAppropriateSelectAccount2? = null
    override val layout: Int
        get() = R.layout.wisdom_appropriate_money_activity

    override fun bindView(bundle: Bundle?) {
        appToolber.tvRight.setOnClickListener {
            WisdomAppropriateMoneyExplainActivity.startContentUI(
                this@WisdomAppropriateMoneyActivity
            )
        }
        tvAllMoney.setOnClickListener(this)
        tvCommit.setOnClickListener(this)
        ivCustomerService.setOnClickListener(this)

        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            onItemClickListener = this@WisdomAppropriateMoneyActivity
        }
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@WisdomAppropriateMoneyActivity)
            adapter = mAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(12f)))
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {

        rspWisdomAppropriateSelectAccount2 = intent.getParcelableExtra("data")
        rspWisdomAppropriateSelectAccount2?.let {
            tvShortName.text = it.shortName
            tvDepositMoney.text = "可用余额" + it.depositMoney + "元"
            viewModel?.queryData(it.bookNo, it.fundMode, it.subsidiaryId)
        }

    }

    @LiveDataMatch
    open fun onAppropriateMoneySuccess(data: PageArrayList<RspWisdomAppropriateMoney>) {
        mAdapter.setNewData(data.arrayList)
    }

    @LiveDataMatch
    open fun onAppropriateMoneyTransferSuccess() {
        RxBusEventManager.postEvent("onAppropriateMoneyTransferSuccess")
        finish()
    }

    override fun onItemClick(adapter: BaseQuickAdapter<*, *>?, view: View?, position: Int) {
        mAdapter.reFreshItem(position)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tvAllMoney -> {
                //划拨全部金额
                rspWisdomAppropriateSelectAccount2?.let {
                    etInputMoney.text = Editable.Factory.getInstance().newEditable(it.depositMoney)
                }
            }

            R.id.iv_customer_service -> {
                //联系客服
                val aMainServer = AMainServer.getPluginServer()
                aMainServer?.showLineServerPhone(this)
            }

            R.id.tv_commit -> {
                val req = ReqWisdomAppropriateMoneyTransfer()
                //提交划拨资金
                val appropriateMoney = etInputMoney.text.toString().trim()
                if (TextUtils.isEmpty(appropriateMoney)) {
                    showDialogToast("请输入转出金额!")
                    return
                }
                val toDouble = appropriateMoney.toDouble()
                if (toDouble <= 0.00) {
                    showDialogToast("请输入正确转出金额!")
                    return
                }
                rspWisdomAppropriateSelectAccount2?.let {
                    val depositMoney = it.depositMoney
                    val toDouble1 = depositMoney.toDouble()
                    if (toDouble > toDouble1) {
                        //表示划拨金额 超过可用金额
                        showDialogToast("转出金额超出可用余额!")
                        return
                    }
                }
                val rspWisdomCutAccount = mAdapter.rspWisdomCutAccount
                if (TextUtils.isEmpty(rspWisdomCutAccount.bookNo)) {
                    showDialogToast("请选择划拨至哪个账户!")
                    return
                }

                req.targetMoney = appropriateMoney
                rspWisdomAppropriateSelectAccount2?.let {
                    req.bookNo = it.bookNo
                    req.fundMode = it.fundMode
                }

                req.targetFundMode = rspWisdomCutAccount.fundMode
                req.targetBookNo = rspWisdomCutAccount.bookNo

                viewModel?.transferMoney(req)

            }
        }
    }

    companion object {
        fun startContentUI(activity: Context, data: RspWisdomAppropriateSelectAccount2?) {
            val intent = Intent(activity, WisdomAppropriateMoneyActivity::class.java)
            intent.putExtra("data", data)
            activity.startActivity(intent)
        }
    }

}