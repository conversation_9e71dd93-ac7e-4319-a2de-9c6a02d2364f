package com.zczy.plugin.wisdom.moreaccount

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.base.WisdomBaseActivity

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/513:24
 *    desc   : 切换其他账户
 *    version: 1.0
 */
class WisdomAppropriateMoneyExplainActivity : WisdomBaseActivity<BaseViewModel>(), View.OnClickListener {


    private val tvKnow by lazy { findViewById<TextView>(R.id.tv_know) }
    private val tv1 by lazy { findViewById<TextView>(R.id.tv1) }

    override val layout: Int
        get() = R.layout.wisdom_appropriate_money_explain_activity

    override fun bindView(bundle: Bundle?) {
        val sp1 = SpannableString("1、同一个签约公司名下的子账户余额可以相互划转，")
        val sp2 = SpannableString("不同签约公司名下资账户余额不可以直接划转；")
        sp2.setSpan(ForegroundColorSpan(Color.parseColor("#FF602E")), 0, sp2.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val ssb = SpannableStringBuilder()
        ssb.append(sp1)
        ssb.append(sp2)
        tv1.text = ssb
        tvKnow.setOnClickListener(this)
    }

    override fun initData() {
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tv_know -> {
                finish()
            }
        }
    }

    companion object {
        fun startContentUI(activity: Context) {
            val intent = Intent(activity, WisdomAppropriateMoneyExplainActivity::class.java)
            activity.startActivity(intent)
        }
    }

}