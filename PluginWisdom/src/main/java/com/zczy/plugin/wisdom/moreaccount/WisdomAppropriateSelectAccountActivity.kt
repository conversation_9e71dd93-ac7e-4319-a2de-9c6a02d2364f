package com.zczy.plugin.wisdom.moreaccount

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.base.WisdomBaseActivity
import com.zczy.plugin.wisdom.moreaccount.adapter.WisdomAppropriateSelectAccountAdapter
import com.zczy.plugin.wisdom.moreaccount.module.WisdomAppropriateSelectAccountModule
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomAppropriateSelectAccount2

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/613:52
 *    desc   :
 *    version: 1.0
 */
open class WisdomAppropriateSelectAccountActivity : WisdomBaseActivity<WisdomAppropriateSelectAccountModule>(), BaseQuickAdapter.OnItemChildClickListener {

    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val recyclerView by lazy { findViewById<RecyclerView>(R.id.recyclerView) }
    private val mAdapter: WisdomAppropriateSelectAccountAdapter = WisdomAppropriateSelectAccountAdapter()

    override val layout: Int
        get() = R.layout.wisdom_appropriate_select_account_activity

    override fun initData() {
        viewModel?.queryData()
    }

    override fun bindView(bundle: Bundle?) {

        appToolber.setRightOnClickListener {
            WisdomAppropriateMoneyExplainActivity.startContentUI(this@WisdomAppropriateSelectAccountActivity)
        }

        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            onItemChildClickListener = this@WisdomAppropriateSelectAccountActivity
        }
        recyclerView.apply {
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(this@WisdomAppropriateSelectAccountActivity)
            adapter = mAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
        }

    }

    override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>?, view: View?, position: Int) {
        //响应资金划拨事件
        val item = mAdapter.getItem(position)
        WisdomAppropriateMoneyActivity.startContentUI(this@WisdomAppropriateSelectAccountActivity, item)
    }

    @LiveDataMatch
    open fun onSuccess(data: List<RspWisdomAppropriateSelectAccount2>) {
        mAdapter.setNewData(data)
    }

    @RxBusEvent(from = "划拨资金转出页面")
    open fun onAppropriateMoneySuccess(content: String) {
        if (TextUtils.equals(content, "onAppropriateMoneyTransferSuccess")) {
            //划拨资金转出成功
            viewModel?.queryData()
        }
    }

    companion object {
        fun startContentUI(context: Context) {
            val intent = Intent(context, WisdomAppropriateSelectAccountActivity::class.java)
            context.startActivity(intent)
        }
    }


}