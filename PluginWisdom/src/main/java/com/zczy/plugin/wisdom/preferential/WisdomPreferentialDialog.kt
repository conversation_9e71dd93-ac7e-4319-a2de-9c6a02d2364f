package com.zczy.plugin.wisdom.preferential

import android.os.Bundle
import android.view.View
import com.zczy.comm.ui.BaseDialog
import com.zczy.plugin.wisdom.R
import kotlinx.android.synthetic.main.wisdom_preferential_dialog.view.*

/**
 * PS: 预付运单
 * Created by sdx on 2019/2/15.
 */
class WisdomPreferentialDialog : BaseDialog() {

    override fun getDialogTag(): String = "DeliverNewGoodsAdvanceDialog"

    override fun getDialogLayout(): Int = R.layout.wisdom_preferential_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        initView(view)
    }

    private fun initView(view: View) {
        view.btn_right.setOnClickListener {
            dismiss()
        }
    }

}