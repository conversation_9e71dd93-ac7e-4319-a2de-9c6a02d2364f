package com.zczy.plugin.wisdom

import android.content.Context
import androidx.fragment.app.Fragment
import com.zczy.cargo_owner.libcomm.IWisdomProvider
import com.zczy.plugin.wisdom.bond.WisdomBondListActivity
import com.zczy.plugin.wisdom.password.WisdomCheckMobileActivity

/**
 *  desc:
 *  user: 宋双朋
 *  time: 2024/12/20 11:53
 */
class WisdomProviderImp : IWisdomProvider {
    override fun openWisdomBondListActivity(fragment: Fragment, bookNo: String, fundMode: String) {
        WisdomBondListActivity.jumpPage(context = fragment.context, bookNo = bookNo, fundMode = fundMode)
    }

    override fun openCheckMobile(context: Context?, type: String) {
        WisdomCheckMobileActivity.startContentUI(context, type)
    }
}