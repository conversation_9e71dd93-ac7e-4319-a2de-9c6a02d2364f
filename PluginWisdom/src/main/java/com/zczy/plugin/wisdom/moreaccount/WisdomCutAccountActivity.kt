package com.zczy.plugin.wisdom.moreaccount

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.base.WisdomBaseActivity
import com.zczy.plugin.wisdom.moreaccount.adapter.WisdomCutAccountAdapter
import com.zczy.plugin.wisdom.moreaccount.module.WisdomCutAccountModule
import com.zczy.plugin.wisdom.moreaccount.req.PageArrayList
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomCutAccount

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/513:24
 *    desc   : 切换其他账户
 *    version: 1.0
 */
open class WisdomCutAccountActivity : WisdomBaseActivity<WisdomCutAccountModule>(), View.OnClickListener {
    override fun onClick(v: View) {
        when (v.id) {
            R.id.tvSure -> {
                val account = mAdapter.rspWisdomCutAccount
                if (TextUtils.isEmpty(account.bookNo)) {
                    return
                }
                RxBusEventManager.postEvent(account)
                finish()
            }
            R.id.tvClose -> {
                finish()
            }
        }
    }

    private val tvClose by lazy { findViewById<TextView>(R.id.tvClose) }
    private val tvSure by lazy { findViewById<TextView>(R.id.tvSure) }
    private val recyclerView by lazy { findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerView) }
    private val mAdapter = WisdomCutAccountAdapter()
    override val layout: Int
        get() = R.layout.wisdom_cut_account_activity

    override fun bindView(bundle: Bundle?) {
        mAdapter.bindToRecyclerView(recyclerView)
        recyclerView.layoutManager =
            androidx.recyclerview.widget.LinearLayoutManager(this)
        recyclerView.adapter = mAdapter
        recyclerView.addItemDecoration(SpaceItemDecoration(dp2px(14f)))
        mAdapter.onItemClickListener = BaseQuickAdapter.OnItemClickListener { _, _, position -> mAdapter.reFreshItem(position) }

        tvClose.setOnClickListener(this)
        tvSure.setOnClickListener(this)
    }

    override fun initData() {
        var fundMode = intent.getStringExtra("fundMode")
        var bookNo = intent.getStringExtra("bookNo")
        if (TextUtils.isEmpty(fundMode)) {
            fundMode = ""
        }
        if (TextUtils.isEmpty(bookNo)) {
            bookNo = ""
        }
        viewModel?.queryData(bookNo?:"", fundMode?:"")
    }

    @LiveDataMatch
    open fun onCutAccountSuccess(data: PageArrayList<RspWisdomCutAccount>) {
        mAdapter.setNewData(data.arrayList)
    }

    companion object {
        fun startContentUI(activity: androidx.fragment.app.FragmentActivity, bookNo: String?, fundMode: String?, requesCode: Int) {
            val intent = Intent(activity, WisdomCutAccountActivity::class.java)
            intent.putExtra("bookNo", bookNo)
            intent.putExtra("fundMode", fundMode)
            activity.startActivityForResult(intent, requesCode)
        }
    }

}