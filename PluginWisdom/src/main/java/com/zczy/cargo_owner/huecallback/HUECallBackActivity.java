package com.zczy.cargo_owner.huecallback;

import com.sfh.lib.ui.AbstractLifecycleActivity;

/**
 * 功能描述: hue回调
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/23 11:43
 */
public class HUECallBackActivity extends AbstractLifecycleActivity {
//
//    String TAG = "HUECallBackActivity";
//    Context context;
//    public static final String HUE_STATUS = "HUEStatus";
//    public static final String HUE_MESSAGE = "HUEMessage";
//    public static final String HUE_TOKEN = "HUEToken";
//    public static final String HUE_SCENARIOId = "HUEScenarioId";
//    /**
//     * 状态码
//     */
//    private int hue_status;
//    /**
//     * 消息
//     */
//    private String hue_message;
//    /**
//     * token
//     */
//    private String hue_token;
//    /**
//     * 场景类型
//     */
//    private String hue_scenarioid;
//
//
//    @Override
//    public void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//
//        DebugLog.i("hue", TAG + " onCreate");
//        context = this;
//        hue_status = getIntent().getIntExtra(HUE_STATUS, 0);
//        hue_message = getIntent().getStringExtra(HUE_MESSAGE);
//        hue_token = getIntent().getStringExtra(HUE_TOKEN);
//        hue_scenarioid = getIntent().getStringExtra(HUE_SCENARIOId);
//
//        DebugLog.i("hue", TAG + " onCreate hue_status= " + hue_status);
//        DebugLog.i("hue", TAG + " onCreate hue_message= " + hue_message);
//        DebugLog.i("hue", TAG + " onCreate hue_token= " + hue_token);
//        DebugLog.i("hue", TAG + " onCreate hue_scenarioid= " + hue_scenarioid);
//
//        if (!TextUtils.isEmpty(hue_message)) {
//            ToastUtil.showToast(context, hue_message);
//        }
//        jumpToPage();
//
//    }
//
//    public void jumpToPage() {
//
//        if (HUESDKConstants.transfer.equals(hue_scenarioid)) {
//            //提现transfer
//            hue_token = getIntent().getStringExtra(HUE_TOKEN);
//            WisdomCashSuccessActivity.startContentUI(this, hue_token);
//            finish();
//        } else if (HUESDKConstants.scantransfer.equals(hue_scenarioid)) {
//            //scanTransfer =  hue_scenarioid  跳转首页
//            DebugLog.i("hue", TAG + " onCreate hue_scenarioid= " + hue_scenarioid);
//            //如果用户已经拒绝，则不用调此接口
//            if (hue_status != HUEConfirmBroadcastModel.AUTH_PUSH_REFUSED) {
//                //在这里处理扫码提现结果
//                if (HUESdkScanController.scanAuthStatus_notTheSame == hue_status) {
//                    //扫码提现时账号已绑定其他设备，需要提示用户是否更换绑定设备  MyZhiYunBaoWebCashUserBind.startUi(this);
//                    finish();
//                }
//                finish();
//            } else {//用户已经拒绝,回到首页
//                //用户拒绝回到提现业务页面，关闭当前activity即可
//                finish();
//            }
//
//        } else if (HUESDKConstants.rebind.equals(hue_scenarioid)) {
//            //更换绑定设备
//            //之前是在这里处理更换绑定设备成功后的结果，现在在回调里面进行处理
//        } else {
//            Toast.makeText(HUECallBackActivity.this, hue_message, Toast.LENGTH_SHORT).show();
//            //解绑
//            finish();
//        }
//
//    }

}
