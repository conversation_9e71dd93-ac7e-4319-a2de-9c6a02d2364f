package com.zczy.cargo_owner.wxapi;


import android.content.Intent;
import android.os.Bundle;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.zczy.plugin.wisdom.postdata.RxWXResultData;

public class WXPayEntryActivity extends AbstractLifecycleActivity implements IWXAPIEventHandler {

    private static final String TAG = "MicroMsg.SDKSample.WXPayEntryActivity";

    private IWXAPI api;
    /**
     * APP_ID 替换为你的应用从官方网站申请到的合法appId
     */
    public static final String APP_ID = "wxd930ea5d5a258f4f";

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = WXAPIFactory.createWXAPI(this, APP_ID);
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
    }

    @Override
    public void onResp(BaseResp resp) {
        if (0 == resp.errCode) {
            //操作成功调用后台回调接口
            RxBusEventManager.postEvent(new RxWXResultData(true));
            finish();
        } else {
            RxBusEventManager.postEvent(new RxWXResultData(true));
            finish();
        }
    }
}