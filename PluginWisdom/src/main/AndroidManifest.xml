<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zczy.plugin.wisdom">

    <!--HUE权限-->
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name=".android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <!--HUE权限-->

    <application>

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <!--...................................................................................微信支付回调.............................................................-->
        <activity
            android:name="com.zczy.cargo_owner.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" />
        <!--...................................................................................微信支付回调.............................................................-->

        <!--设置支付密码-->
        <activity
            android:name=".password.WisdomSetPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--修改支付密码-->
        <activity
            android:name=".password.WisdomEditPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--忘记支付密码-->
        <activity
            android:name=".password.WisdomForgetPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--支付密码管理-->
        <activity
            android:name=".password.WisdomManagePwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--支付密码管理-->
        <activity
            android:name=".home.WisdomManageActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--发送短信验证码-->
        <activity
            android:name=".password.WisdomCheckMobileActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--添加对公银行卡-->
        <activity
            android:name=".bank.WisdomAddPublicBankActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--收支明细列表-->
        <activity
            android:name=".budget.WisdomBudgetListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--收支明细详情-->
        <activity
            android:name=".budget.WisdomBudgetDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--收支明细详情-->
        <activity
            android:name=".budget.WisdomBudgetSearchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--选择时间-->
        <activity
            android:name=".date.WisdomSelectDateActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--银行卡列表-->
        <activity
            android:name=".bank.WisdomBankListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--结算金额列表-->
        <activity
            android:name=".settle.WisdomSettleActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--结算金额详情-->
        <activity
            android:name=".settle.WisdomSettleDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--冻结金额-->
        <activity
            android:name=".settle.WisdomSettleBondActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--冻结金额-->
        <activity
            android:name=".bond.WisdomBondListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--冻结金额-->
        <activity
            android:name=".bond.WisdomBondSearchListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--冻结金额详情-->
        <activity
            android:name=".settle.WisdomSettleBondDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--充值-->
        <activity
            android:name=".home.WisdomRechargeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--充值web页-->
        <activity
            android:name=".home.WisdomRechargeBankActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--提现-->
        <activity
            android:name=".cash.WisdomCashActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--提现结果页-->
        <activity
            android:name=".cash.WisdomCashSuccessActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--修改手势密码-->
        <activity
            android:name=".password.WisdomManageHuePwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--未结算金额列表-->
        <activity
            android:name=".unsettle.WisdomUnSettleActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--未结算金额详情-->
        <activity
            android:name=".unsettle.WisdomUnSettleDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--提现详情-->
        <activity
            android:name=".budget.WisdomCashDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--充值详情-->
        <activity
            android:name=".budget.WisdomRechargeDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--余额不足页面-->
        <activity
            android:name=".home.WisdomAccoutActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--        <activity android:name=".home.HUEBindActivity" />-->
        <activity
            android:name=".bank.WisdomBankCheckFailedActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".bank.WisdomBankCheckSuccessActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".bank.WisdomBankCheckMoneyActivity"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".scancash.WisdomScanActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".scancash.WisdomScanCashActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".moreaccount.WisdomCutAccountActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".moreaccount.WisdomAppropriateMoneyExplainActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".moreaccount.WisdomAppropriateSelectAccountActivity"
            android:launchMode="singleTask" />
        <activity android:name=".moreaccount.WisdomAppropriateMoneyActivity" />
        <!--绑卡步骤提醒-->
        <activity
            android:name=".bank.MoneyCheckDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/MyDialogStyleBottom" />
        <!--免诚意金提醒-->
        <activity
            android:name=".earnest.FreeEarnestMoneyMainActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".earnest.FreeEarnestMoneyApplyActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".preferential.WisdomPreferentialActivity"
            android:label="奖励金额"
            android:launchMode="singleTask" />
        <activity
            android:name=".surplus.WisdomSurplusBackActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".surplus.WisdomSurplusBackDetailActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".home.WisdomHomeFilterActivity2"
            android:label="筛选"
            android:launchMode="singleTask" />
    </application>

</manifest>